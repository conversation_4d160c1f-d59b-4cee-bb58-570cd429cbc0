package org.project;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;

/**
 * Service class to fetch location data from IP geolocation APIs
 */
public class LocationService {
    
    private static final String IPINFO_API_URL = "https://ipinfo.io/json";
    private static final String IPAPI_API_URL = "https://ip-api.com/json/";
    private static final String IPIFY_API_URL = "https://api.ipify.org?format=json";

    // Backup APIs for better reliability
    private static final String IPAPI_BACKUP_URL = "http://ip-api.com/json/";
    private static final String IPGEOLOCATION_API_URL = "https://api.ipgeolocation.io/ipgeo?apiKey=";

    private final HttpClient httpClient;
    private final Gson gson;
    private static final int MAX_RETRIES = 3;
    private static final int RETRY_DELAY_MS = 1000;

    public LocationService() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(30))
                .build();
        this.gson = new Gson();
    }
    
    /**
     * Get the public IP address of the user with retry mechanism
     */
    public String getPublicIpAddress() throws IOException, InterruptedException {
        return executeWithRetry(() -> {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(IPIFY_API_URL))
                    .timeout(Duration.ofSeconds(30))
                    .header("User-Agent", "Java-HttpClient/11")
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                JsonObject jsonObject = JsonParser.parseString(response.body()).getAsJsonObject();
                return jsonObject.get("ip").getAsString();
            } else {
                throw new IOException("Failed to get IP address. Status code: " + response.statusCode());
            }
        });
    }
    
    /**
     * Fetch location data using ipinfo.io API with retry mechanism
     */
    public LocationData getLocationFromIpInfo() throws IOException, InterruptedException {
        return executeWithRetry(() -> {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(IPINFO_API_URL))
                    .timeout(Duration.ofSeconds(30))
                    .header("User-Agent", "Java-HttpClient/11")
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                return parseIpInfoResponse(response.body());
            } else {
                throw new IOException("Failed to get location data from ipinfo.io. Status code: " + response.statusCode());
            }
        });
    }
    
    /**
     * Fetch location data using ip-api.com API with retry mechanism
     */
    public LocationData getLocationFromIpApi() throws IOException, InterruptedException {
        return executeWithRetry(() -> {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create(IPAPI_API_URL))
                    .timeout(Duration.ofSeconds(30))
                    .header("User-Agent", "Java-HttpClient/11")
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

            if (response.statusCode() == 200) {
                return parseIpApiResponse(response.body());
            } else {
                throw new IOException("Failed to get location data from ip-api.com. Status code: " + response.statusCode());
            }
        });
    }
    
    /**
     * Fetch location data for a specific IP address with retry and fallback
     */
    public LocationData getLocationForIp(String ipAddress) throws IOException, InterruptedException {
        // Try HTTPS first, then HTTP as fallback
        String[] urls = {IPAPI_API_URL + ipAddress, IPAPI_BACKUP_URL + ipAddress};

        for (String url : urls) {
            try {
                return executeWithRetry(() -> {
                    HttpRequest request = HttpRequest.newBuilder()
                            .uri(URI.create(url))
                            .timeout(Duration.ofSeconds(30))
                            .header("User-Agent", "Java-HttpClient/11")
                            .GET()
                            .build();

                    HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());

                    if (response.statusCode() == 200) {
                        return parseIpApiResponse(response.body());
                    } else {
                        throw new IOException("Failed to get location data for IP: " + ipAddress + ". Status code: " + response.statusCode());
                    }
                });
            } catch (Exception e) {
                System.err.println("Failed with URL " + url + ": " + e.getMessage());
                if (url.equals(urls[urls.length - 1])) {
                    // This was the last URL, rethrow the exception
                    throw e;
                }
                // Continue to next URL
            }
        }

        throw new IOException("All APIs failed for IP: " + ipAddress);
    }
    
    /**
     * Parse response from ipinfo.io API
     */
    private LocationData parseIpInfoResponse(String jsonResponse) {
        JsonObject jsonObject = JsonParser.parseString(jsonResponse).getAsJsonObject();
        LocationData locationData = new LocationData();
        
        locationData.setIp(getStringValue(jsonObject, "ip"));
        locationData.setCity(getStringValue(jsonObject, "city"));
        locationData.setRegion(getStringValue(jsonObject, "region"));
        locationData.setCountry(getStringValue(jsonObject, "country"));
        locationData.setTimezone(getStringValue(jsonObject, "timezone"));
        locationData.setOrganization(getStringValue(jsonObject, "org"));
        locationData.setPostal(getStringValue(jsonObject, "postal"));
        
        // Parse location coordinates if available
        String loc = getStringValue(jsonObject, "loc");
        if (loc != null && loc.contains(",")) {
            String[] coords = loc.split(",");
            if (coords.length == 2) {
                locationData.setLatitude(coords[0].trim());
                locationData.setLongitude(coords[1].trim());
            }
        }
        
        return locationData;
    }
    
    /**
     * Parse response from ip-api.com API
     */
    private LocationData parseIpApiResponse(String jsonResponse) {
        JsonObject jsonObject = JsonParser.parseString(jsonResponse).getAsJsonObject();
        LocationData locationData = new LocationData();
        
        // Check if the request was successful
        String status = getStringValue(jsonObject, "status");
        if (!"success".equals(status)) {
            throw new RuntimeException("API request failed: " + getStringValue(jsonObject, "message"));
        }
        
        locationData.setIp(getStringValue(jsonObject, "query"));
        locationData.setCity(getStringValue(jsonObject, "city"));
        locationData.setRegion(getStringValue(jsonObject, "regionName"));
        locationData.setCountry(getStringValue(jsonObject, "country"));
        locationData.setCountryCode(getStringValue(jsonObject, "countryCode"));
        locationData.setTimezone(getStringValue(jsonObject, "timezone"));
        locationData.setIsp(getStringValue(jsonObject, "isp"));
        locationData.setOrganization(getStringValue(jsonObject, "org"));
        locationData.setPostal(getStringValue(jsonObject, "zip"));
        
        // Parse coordinates
        if (jsonObject.has("lat")) {
            locationData.setLatitude(String.valueOf(jsonObject.get("lat").getAsDouble()));
        }
        if (jsonObject.has("lon")) {
            locationData.setLongitude(String.valueOf(jsonObject.get("lon").getAsDouble()));
        }
        
        return locationData;
    }
    
    /**
     * Safely extract string value from JSON object
     */
    private String getStringValue(JsonObject jsonObject, String key) {
        if (jsonObject.has(key) && !jsonObject.get(key).isJsonNull()) {
            return jsonObject.get(key).getAsString();
        }
        return null;
    }
    
    /**
     * Test network connectivity
     */
    public boolean testConnectivity() {
        try {
            HttpRequest request = HttpRequest.newBuilder()
                    .uri(URI.create("https://www.google.com"))
                    .timeout(Duration.ofSeconds(10))
                    .header("User-Agent", "Java-HttpClient/11")
                    .GET()
                    .build();

            HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
            return response.statusCode() == 200;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Get location data with fallback to multiple APIs and better error handling
     */
    public LocationData getLocationWithFallback() throws IOException, InterruptedException {
        // First check network connectivity
        if (!testConnectivity()) {
            throw new IOException("No internet connection detected. Please check your network connection.");
        }

        Exception lastException = null;

        try {
            // Try ip-api.com first (more detailed information)
            System.out.println("Trying ip-api.com...");
            return getLocationFromIpApi();
        } catch (Exception e) {
            lastException = e;
            System.err.println("Failed to get location from ip-api.com: " + e.getMessage());

            try {
                // Fallback to ipinfo.io
                System.out.println("Trying ipinfo.io...");
                return getLocationFromIpInfo();
            } catch (Exception e2) {
                lastException = e2;
                System.err.println("Failed to get location from ipinfo.io: " + e2.getMessage());
            }
        }

        // If we get here, all APIs failed
        String errorMessage = "All location APIs failed. ";
        if (lastException != null) {
            if (lastException.getMessage().contains("timed out")) {
                errorMessage += "Connection timeout - please check your internet connection and try again.";
            } else if (lastException.getMessage().contains("UnknownHostException")) {
                errorMessage += "DNS resolution failed - please check your internet connection.";
            } else {
                errorMessage += "Last error: " + lastException.getMessage();
            }
        }

        throw new IOException(errorMessage, lastException);
    }
    
    /**
     * Execute HTTP request with retry mechanism
     */
    private <T> T executeWithRetry(HttpCallable<T> callable) throws IOException, InterruptedException {
        Exception lastException = null;

        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                return callable.call();
            } catch (Exception e) {
                lastException = e;
                System.err.println("Attempt " + attempt + " failed: " + e.getMessage());

                if (attempt < MAX_RETRIES) {
                    System.out.println("Retrying in " + RETRY_DELAY_MS + "ms...");
                    Thread.sleep(RETRY_DELAY_MS);
                }
            }
        }

        // All retries failed
        if (lastException instanceof IOException) {
            throw (IOException) lastException;
        } else if (lastException instanceof InterruptedException) {
            throw (InterruptedException) lastException;
        } else if (lastException instanceof RuntimeException) {
            throw (RuntimeException) lastException;
        } else {
            throw new IOException("Request failed after " + MAX_RETRIES + " attempts", lastException);
        }
    }

    /**
     * Functional interface for HTTP calls that can throw exceptions
     */
    @FunctionalInterface
    private interface HttpCallable<T> {
        T call() throws Exception;
    }

    /**
     * Close the HTTP client
     */
    public void close() {
        // HttpClient doesn't need explicit closing in Java 11+
        // but this method is provided for consistency
    }
}
