package org.project;

import java.io.IOException;
import java.util.Scanner;

/**
 * Console-based application for IP geolocation lookup
 */
public class ConsoleLocationApp {
    
    private final LocationService locationService;
    private final Scanner scanner;
    
    public ConsoleLocationApp() {
        this.locationService = new LocationService();
        this.scanner = new Scanner(System.in);
    }
    
    public void run() {
        System.out.println("=================================");
        System.out.println("   IP Geolocation Finder");
        System.out.println("=================================");
        System.out.println();
        
        while (true) {
            displayMenu();
            int choice = getChoice();
            
            switch (choice) {
                case 1:
                    getCurrentLocation();
                    break;
                case 2:
                    lookupSpecificIp();
                    break;
                case 3:
                    getPublicIp();
                    break;
                case 4:
                    System.out.println("Thank you for using IP Geolocation Finder!");
                    locationService.close();
                    return;
                default:
                    System.out.println("Invalid choice. Please try again.");
            }
            
            System.out.println("\nPress Enter to continue...");
            scanner.nextLine();
        }
    }
    
    private void displayMenu() {
        System.out.println("\n--- Main Menu ---");
        System.out.println("1. Get my current location");
        System.out.println("2. Lookup specific IP address");
        System.out.println("3. Get my public IP address");
        System.out.println("4. Exit");
        System.out.print("Enter your choice (1-4): ");
    }
    
    private int getChoice() {
        try {
            String input = scanner.nextLine().trim();
            return Integer.parseInt(input);
        } catch (NumberFormatException e) {
            return -1;
        }
    }
    
    private void getCurrentLocation() {
        System.out.println("\n--- Getting Your Current Location ---");
        System.out.println("Fetching location data...");
        
        try {
            LocationData location = locationService.getLocationWithFallback();
            displayLocationData(location);
        } catch (IOException | InterruptedException e) {
            System.err.println("Error fetching location: " + e.getMessage());
            
            // Try alternative approach
            System.out.println("Trying alternative method...");
            try {
                LocationData location = locationService.getLocationFromIpInfo();
                displayLocationData(location);
            } catch (IOException | InterruptedException e2) {
                System.err.println("All attempts failed: " + e2.getMessage());
            }
        }
    }
    
    private void lookupSpecificIp() {
        System.out.println("\n--- Lookup Specific IP Address ---");
        System.out.print("Enter IP address (e.g., *******): ");
        String ipAddress = scanner.nextLine().trim();

        if (ipAddress.isEmpty()) {
            System.out.println("No IP address entered.");
            return;
        }

        // Basic IP address validation
        if (!isValidIpAddress(ipAddress)) {
            System.out.println("Invalid IP address format.");
            return;
        }

        System.out.println("Fetching location data for " + ipAddress + "...");
        System.out.println("This may take a few moments, please wait...");

        try {
            // Test connectivity first
            System.out.print("Testing network connectivity... ");
            if (!locationService.testConnectivity()) {
                System.out.println("FAILED");
                System.err.println("No internet connection detected. Please check your network connection.");
                return;
            }
            System.out.println("OK");

            LocationData location = locationService.getLocationForIp(ipAddress);
            displayLocationData(location);
        } catch (IOException | InterruptedException e) {
            System.err.println("Error fetching location for " + ipAddress + ": " + e.getMessage());

            // Provide helpful suggestions
            if (e.getMessage().contains("timed out")) {
                System.out.println("\nTroubleshooting suggestions:");
                System.out.println("1. Check your internet connection");
                System.out.println("2. Try again in a few moments");
                System.out.println("3. Check if your firewall is blocking the connection");
            }
        }
    }
    
    private void getPublicIp() {
        System.out.println("\n--- Getting Your Public IP Address ---");
        System.out.println("Fetching public IP address...");
        
        try {
            String publicIp = locationService.getPublicIpAddress();
            System.out.println("Your public IP address: " + publicIp);
        } catch (IOException | InterruptedException e) {
            System.err.println("Error fetching public IP address: " + e.getMessage());
        }
    }
    
    private void displayLocationData(LocationData location) {
        if (location == null) {
            System.out.println("No location data available.");
            return;
        }
        
        System.out.println("\n--- Location Information ---");
        System.out.println(location.getDisplayString());
    }
    
    private boolean isValidIpAddress(String ip) {
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        
        String[] parts = ip.split("\\.");
        if (parts.length != 4) {
            return false;
        }
        
        try {
            for (String part : parts) {
                int num = Integer.parseInt(part);
                if (num < 0 || num > 255) {
                    return false;
                }
            }
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    public static void main(String[] args) {
        ConsoleLocationApp app = new ConsoleLocationApp();
        app.run();
    }
}
