package org.project;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;

import java.io.IOException;

/**
 * Unit tests for LocationService class
 * Note: These tests primarily test the parsing logic and error handling.
 * Network-dependent methods are tested with integration-style tests.
 */
@DisplayName("LocationService Tests")
@ExtendWith(MockitoExtension.class)
class LocationServiceTest {

    private LocationService locationService;

    @BeforeEach
    void setUp() {
        locationService = new LocationService();
    }

    @Test
    @DisplayName("LocationService can be instantiated")
    void testLocationServiceInstantiation() {
        assertNotNull(locationService);
    }

    @Test
    @DisplayName("Close method executes without error")
    void testCloseMethod() {
        assertDoesNotThrow(() -> locationService.close());
    }

    @Test
    @DisplayName("parseIpInfoResponse handles valid JSON correctly")
    void testParseIpInfoResponseValidJson() throws Exception {
        String validJson = """
            {
                "ip": "*******",
                "city": "Mountain View",
                "region": "California",
                "country": "US",
                "loc": "37.4056,-122.0775",
                "org": "AS15169 Google LLC",
                "postal": "94043",
                "timezone": "America/Los_Angeles"
            }
            """;

        // Use reflection to access private method for testing
        java.lang.reflect.Method parseMethod = LocationService.class.getDeclaredMethod("parseIpInfoResponse", String.class);
        parseMethod.setAccessible(true);
        LocationData result = (LocationData) parseMethod.invoke(locationService, validJson);

        assertNotNull(result);
        assertEquals("*******", result.getIp());
        assertEquals("Mountain View", result.getCity());
        assertEquals("California", result.getRegion());
        assertEquals("US", result.getCountry());
        assertEquals("37.4056", result.getLatitude());
        assertEquals("-122.0775", result.getLongitude());
        assertEquals("AS15169 Google LLC", result.getOrganization());
        assertEquals("94043", result.getPostal());
        assertEquals("America/Los_Angeles", result.getTimezone());
    }

    @Test
    @DisplayName("parseIpInfoResponse handles JSON without location coordinates")
    void testParseIpInfoResponseWithoutCoordinates() throws Exception {
        String jsonWithoutLoc = """
            {
                "ip": "*******",
                "city": "Mountain View",
                "region": "California",
                "country": "US",
                "org": "AS15169 Google LLC",
                "postal": "94043",
                "timezone": "America/Los_Angeles"
            }
            """;

        java.lang.reflect.Method parseMethod = LocationService.class.getDeclaredMethod("parseIpInfoResponse", String.class);
        parseMethod.setAccessible(true);
        LocationData result = (LocationData) parseMethod.invoke(locationService, jsonWithoutLoc);

        assertNotNull(result);
        assertEquals("*******", result.getIp());
        assertEquals("Mountain View", result.getCity());
        assertNull(result.getLatitude());
        assertNull(result.getLongitude());
    }

    @Test
    @DisplayName("parseIpApiResponse handles valid successful JSON correctly")
    void testParseIpApiResponseValidJson() throws Exception {
        String validJson = """
            {
                "status": "success",
                "country": "United States",
                "countryCode": "US",
                "region": "CA",
                "regionName": "California",
                "city": "Mountain View",
                "zip": "94043",
                "lat": 37.4056,
                "lon": -122.0775,
                "timezone": "America/Los_Angeles",
                "isp": "Google LLC",
                "org": "Google Public DNS",
                "query": "*******"
            }
            """;

        java.lang.reflect.Method parseMethod = LocationService.class.getDeclaredMethod("parseIpApiResponse", String.class);
        parseMethod.setAccessible(true);
        LocationData result = (LocationData) parseMethod.invoke(locationService, validJson);

        assertNotNull(result);
        assertEquals("*******", result.getIp());
        assertEquals("Mountain View", result.getCity());
        assertEquals("California", result.getRegion());
        assertEquals("United States", result.getCountry());
        assertEquals("US", result.getCountryCode());
        assertEquals("37.4056", result.getLatitude());
        assertEquals("-122.0775", result.getLongitude());
        assertEquals("Google LLC", result.getIsp());
        assertEquals("Google Public DNS", result.getOrganization());
        assertEquals("94043", result.getPostal());
        assertEquals("America/Los_Angeles", result.getTimezone());
    }

    @Test
    @DisplayName("parseIpApiResponse throws exception for failed status")
    void testParseIpApiResponseFailedStatus() throws Exception {
        String failedJson = """
            {
                "status": "fail",
                "message": "private range",
                "query": "***********"
            }
            """;

        java.lang.reflect.Method parseMethod = LocationService.class.getDeclaredMethod("parseIpApiResponse", String.class);
        parseMethod.setAccessible(true);

        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            parseMethod.invoke(locationService, failedJson);
        });

        assertTrue(exception.getCause().getMessage().contains("API request failed"));
        assertTrue(exception.getCause().getMessage().contains("private range"));
    }

    @Test
    @DisplayName("getStringValue helper method handles null and missing values")
    void testGetStringValueHelper() throws Exception {
        String jsonWithNulls = """
            {
                "existing": "value",
                "nullValue": null
            }
            """;

        com.google.gson.JsonObject jsonObject = com.google.gson.JsonParser.parseString(jsonWithNulls).getAsJsonObject();
        
        java.lang.reflect.Method getStringValueMethod = LocationService.class.getDeclaredMethod("getStringValue", com.google.gson.JsonObject.class, String.class);
        getStringValueMethod.setAccessible(true);

        // Test existing value
        String existingValue = (String) getStringValueMethod.invoke(locationService, jsonObject, "existing");
        assertEquals("value", existingValue);

        // Test null value
        String nullValue = (String) getStringValueMethod.invoke(locationService, jsonObject, "nullValue");
        assertNull(nullValue);

        // Test missing key
        String missingValue = (String) getStringValueMethod.invoke(locationService, jsonObject, "missing");
        assertNull(missingValue);
    }

    @Test
    @DisplayName("parseIpInfoResponse handles malformed location coordinates")
    void testParseIpInfoResponseMalformedCoordinates() throws Exception {
        String jsonWithBadLoc = """
            {
                "ip": "*******",
                "city": "Mountain View",
                "region": "California",
                "country": "US",
                "loc": "invalid-coordinates",
                "org": "AS15169 Google LLC"
            }
            """;

        java.lang.reflect.Method parseMethod = LocationService.class.getDeclaredMethod("parseIpInfoResponse", String.class);
        parseMethod.setAccessible(true);
        LocationData result = (LocationData) parseMethod.invoke(locationService, jsonWithBadLoc);

        assertNotNull(result);
        assertEquals("*******", result.getIp());
        assertNull(result.getLatitude());
        assertNull(result.getLongitude());
    }

    @Test
    @DisplayName("parseIpInfoResponse handles single coordinate in loc field")
    void testParseIpInfoResponseSingleCoordinate() throws Exception {
        String jsonWithSingleCoord = """
            {
                "ip": "*******",
                "city": "Mountain View",
                "region": "California",
                "country": "US",
                "loc": "37.4056",
                "org": "AS15169 Google LLC"
            }
            """;

        java.lang.reflect.Method parseMethod = LocationService.class.getDeclaredMethod("parseIpInfoResponse", String.class);
        parseMethod.setAccessible(true);
        LocationData result = (LocationData) parseMethod.invoke(locationService, jsonWithSingleCoord);

        assertNotNull(result);
        assertEquals("*******", result.getIp());
        assertNull(result.getLatitude());
        assertNull(result.getLongitude());
    }

    @Test
    @DisplayName("parseIpApiResponse handles missing coordinate fields")
    void testParseIpApiResponseMissingCoordinates() throws Exception {
        String jsonWithoutCoords = """
            {
                "status": "success",
                "country": "United States",
                "countryCode": "US",
                "region": "CA",
                "regionName": "California",
                "city": "Mountain View",
                "timezone": "America/Los_Angeles",
                "isp": "Google LLC",
                "query": "*******"
            }
            """;

        java.lang.reflect.Method parseMethod = LocationService.class.getDeclaredMethod("parseIpApiResponse", String.class);
        parseMethod.setAccessible(true);
        LocationData result = (LocationData) parseMethod.invoke(locationService, jsonWithoutCoords);

        assertNotNull(result);
        assertEquals("*******", result.getIp());
        assertEquals("Mountain View", result.getCity());
        assertNull(result.getLatitude());
        assertNull(result.getLongitude());
    }

    @Test
    @DisplayName("parseIpApiResponse handles minimal successful response")
    void testParseIpApiResponseMinimal() throws Exception {
        String minimalJson = """
            {
                "status": "success",
                "query": "*******"
            }
            """;

        java.lang.reflect.Method parseMethod = LocationService.class.getDeclaredMethod("parseIpApiResponse", String.class);
        parseMethod.setAccessible(true);
        LocationData result = (LocationData) parseMethod.invoke(locationService, minimalJson);

        assertNotNull(result);
        assertEquals("*******", result.getIp());
        assertNull(result.getCity());
        assertNull(result.getRegion());
        assertNull(result.getCountry());
    }
}
