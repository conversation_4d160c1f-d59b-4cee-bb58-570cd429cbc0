package org.project;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * Unit tests for LocationData class
 */
@DisplayName("LocationData Tests")
class LocationDataTest {

    private LocationData locationData;

    @BeforeEach
    void setUp() {
        locationData = new LocationData();
    }

    @Test
    @DisplayName("Default constructor creates empty LocationData")
    void testDefaultConstructor() {
        assertNotNull(locationData);
        assertNull(locationData.getIp());
        assertNull(locationData.getCity());
        assertNull(locationData.getRegion());
        assertNull(locationData.getCountry());
        assertNull(locationData.getCountryCode());
        assertNull(locationData.getTimezone());
        assertNull(locationData.getLatitude());
        assertNull(locationData.getLongitude());
        assertNull(locationData.getIsp());
        assertNull(locationData.getOrganization());
        assertNull(locationData.getPostal());
    }

    @Test
    @DisplayName("Constructor with essential fields sets values correctly")
    void testConstructorWithEssentialFields() {
        LocationData data = new LocationData("***********", "New York", "NY", "United States");
        
        assertEquals("***********", data.getIp());
        assertEquals("New York", data.getCity());
        assertEquals("NY", data.getRegion());
        assertEquals("United States", data.getCountry());
    }

    @Test
    @DisplayName("Setters and getters work correctly")
    void testSettersAndGetters() {
        locationData.setIp("*******");
        locationData.setCity("Mountain View");
        locationData.setRegion("California");
        locationData.setCountry("United States");
        locationData.setCountryCode("US");
        locationData.setTimezone("America/Los_Angeles");
        locationData.setLatitude("37.4056");
        locationData.setLongitude("-122.0775");
        locationData.setIsp("Google LLC");
        locationData.setOrganization("Google Public DNS");
        locationData.setPostal("94043");

        assertEquals("*******", locationData.getIp());
        assertEquals("Mountain View", locationData.getCity());
        assertEquals("California", locationData.getRegion());
        assertEquals("United States", locationData.getCountry());
        assertEquals("US", locationData.getCountryCode());
        assertEquals("America/Los_Angeles", locationData.getTimezone());
        assertEquals("37.4056", locationData.getLatitude());
        assertEquals("-122.0775", locationData.getLongitude());
        assertEquals("Google LLC", locationData.getIsp());
        assertEquals("Google Public DNS", locationData.getOrganization());
        assertEquals("94043", locationData.getPostal());
    }

    @Test
    @DisplayName("toString method returns formatted string")
    void testToString() {
        locationData.setIp("*******");
        locationData.setCity("Mountain View");
        locationData.setCountry("United States");
        
        String result = locationData.toString();
        
        assertNotNull(result);
        assertTrue(result.contains("*******"));
        assertTrue(result.contains("Mountain View"));
        assertTrue(result.contains("United States"));
        assertTrue(result.startsWith("LocationData{"));
    }

    @Test
    @DisplayName("getDisplayString returns formatted display string")
    void testGetDisplayString() {
        locationData.setIp("*******");
        locationData.setCity("Mountain View");
        locationData.setRegion("California");
        locationData.setCountry("United States");
        locationData.setCountryCode("US");
        locationData.setTimezone("America/Los_Angeles");
        locationData.setLatitude("37.4056");
        locationData.setLongitude("-122.0775");
        locationData.setIsp("Google LLC");
        locationData.setOrganization("Google Public DNS");
        locationData.setPostal("94043");

        String displayString = locationData.getDisplayString();
        
        assertNotNull(displayString);
        assertTrue(displayString.contains("IP Address: *******"));
        assertTrue(displayString.contains("City: Mountain View"));
        assertTrue(displayString.contains("Region: California"));
        assertTrue(displayString.contains("Country: United States"));
        assertTrue(displayString.contains("Country Code: US"));
        assertTrue(displayString.contains("Timezone: America/Los_Angeles"));
        assertTrue(displayString.contains("Coordinates: 37.4056, -122.0775"));
        assertTrue(displayString.contains("ISP: Google LLC"));
        assertTrue(displayString.contains("Organization: Google Public DNS"));
        assertTrue(displayString.contains("Postal: 94043"));
    }

    @Test
    @DisplayName("getDisplayString handles null values gracefully")
    void testGetDisplayStringWithNullValues() {
        locationData.setIp("*******");
        locationData.setCity("Mountain View");
        // Leave other fields as null
        
        String displayString = locationData.getDisplayString();
        
        assertNotNull(displayString);
        assertTrue(displayString.contains("IP Address: *******"));
        assertTrue(displayString.contains("City: Mountain View"));
        assertTrue(displayString.contains("Region: Unknown"));
        assertTrue(displayString.contains("Country: Unknown"));
        assertFalse(displayString.contains("Country Code:"));
        assertFalse(displayString.contains("Timezone:"));
        assertFalse(displayString.contains("Coordinates:"));
        assertFalse(displayString.contains("ISP:"));
        assertFalse(displayString.contains("Organization:"));
    }

    @Test
    @DisplayName("getDisplayString shows Unknown for null essential fields")
    void testGetDisplayStringWithAllNullValues() {
        String displayString = locationData.getDisplayString();
        
        assertNotNull(displayString);
        assertTrue(displayString.contains("IP Address: Unknown"));
        assertTrue(displayString.contains("City: Unknown"));
        assertTrue(displayString.contains("Region: Unknown"));
        assertTrue(displayString.contains("Country: Unknown"));
    }

    @Test
    @DisplayName("Coordinates are only shown when both latitude and longitude are present")
    void testCoordinatesDisplayLogic() {
        // Test with only latitude
        locationData.setLatitude("37.4056");
        String displayString1 = locationData.getDisplayString();
        assertFalse(displayString1.contains("Coordinates:"));
        
        // Test with only longitude
        locationData.setLatitude(null);
        locationData.setLongitude("-122.0775");
        String displayString2 = locationData.getDisplayString();
        assertFalse(displayString2.contains("Coordinates:"));
        
        // Test with both
        locationData.setLatitude("37.4056");
        locationData.setLongitude("-122.0775");
        String displayString3 = locationData.getDisplayString();
        assertTrue(displayString3.contains("Coordinates: 37.4056, -122.0775"));
    }

    @Test
    @DisplayName("Setters accept null values")
    void testSettersWithNullValues() {
        locationData.setIp("*******");
        locationData.setCity("Test City");
        
        // Set to null
        locationData.setIp(null);
        locationData.setCity(null);
        
        assertNull(locationData.getIp());
        assertNull(locationData.getCity());
    }

    @Test
    @DisplayName("Setters accept empty strings")
    void testSettersWithEmptyStrings() {
        locationData.setIp("");
        locationData.setCity("");
        locationData.setRegion("");
        
        assertEquals("", locationData.getIp());
        assertEquals("", locationData.getCity());
        assertEquals("", locationData.getRegion());
    }
}
